<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Session Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            word-break: break-all;
            max-height: 100px;
            overflow-y: auto;
        }
        .attack-section {
            border: 2px solid #dc3545;
            background: #f8d7da;
        }
        .security-test {
            background: #d4edda;
            border: 2px solid #28a745;
        }
    </style>
</head>
<body>
    <h1>🔒 Secure Session Lock Test</h1>
    <p>This page tests the secure server-side session lock implementation.</p>

    <div class="test-section">
        <h2>Current Status</h2>
        <div id="authStatus" class="status info">Checking authentication...</div>
        <div id="sessionStatus" class="status info">Checking session status...</div>
        <div id="lockToken" class="token-display">No lock token</div>
    </div>

    <div class="test-section">
        <h2>Session Lock Controls</h2>
        <button onclick="lockSession('manual')">Lock Session (Manual)</button>
        <button onclick="lockSession('inactivity')">Lock Session (Inactivity)</button>
        <button onclick="lockSession('security')">Lock Session (Security)</button>
        <button onclick="checkSessionStatus()">Check Session Status</button>
    </div>

    <div class="test-section">
        <h2>Session Unlock Controls</h2>
        <div>
            <label>PIN: <input type="password" id="pinInput" placeholder="Enter PIN" maxlength="6"></label>
            <button onclick="unlockSession('pin')">Unlock with PIN</button>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="unlockSession('biometric')">Unlock with Biometric</button>
        </div>
    </div>

    <div class="test-section attack-section">
        <h2>🚨 Security Attack Tests</h2>
        <p><strong>Try to bypass the session lock using these methods:</strong></p>
        <button onclick="tryLocalStorageBypass()">Try localStorage Bypass</button>
        <button onclick="tryTokenForging()">Try Token Forging</button>
        <button onclick="tryDirectUnlock()">Try Direct Unlock (No Token)</button>
        <button onclick="tryExpiredToken()">Try Expired Token</button>
        <div id="attackResults" class="status info">No attacks attempted yet</div>
    </div>

    <div class="test-section security-test">
        <h2>✅ Security Verification</h2>
        <button onclick="runSecurityTests()">Run All Security Tests</button>
        <div id="securityResults" class="status info">No tests run yet</div>
    </div>

    <div class="test-section">
        <h2>Activity Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:3001';
        const API_VERSION = 'v1';
        
        let currentLockToken = null;
        let authToken = localStorage.getItem('auth_token');

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // API helper function
        async function makeApiCall(method, endpoint, data = null) {
            const url = `${API_BASE_URL}/api/${API_VERSION}${endpoint}`;
            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { Authorization: `Bearer ${authToken}` })
                }
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, config);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${result.error?.message || 'Unknown error'}`);
                }
                
                return result;
            } catch (error) {
                log(`API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        // Update status displays
        function updateAuthStatus() {
            const statusDiv = document.getElementById('authStatus');
            if (authToken) {
                statusDiv.textContent = 'Authenticated ✅';
                statusDiv.className = 'status success';
            } else {
                statusDiv.textContent = 'Not Authenticated ❌';
                statusDiv.className = 'status error';
            }
        }

        function updateLockToken(token) {
            currentLockToken = token;
            const tokenDiv = document.getElementById('lockToken');
            if (token) {
                tokenDiv.textContent = `Lock Token: ${token}`;
                tokenDiv.className = 'token-display';
            } else {
                tokenDiv.textContent = 'No lock token';
                tokenDiv.className = 'token-display';
            }
        }

        // Session lock functions
        async function lockSession(reason) {
            try {
                log(`Attempting to lock session with reason: ${reason}`);
                const response = await makeApiCall('POST', '/auth/session/lock', { reason });
                
                if (response.success) {
                    updateLockToken(response.data.lockToken);
                    log(`✅ Session locked successfully! Reason: ${reason}`, 'success');
                    log(`Lock Token received: ${response.data.lockToken.substring(0, 50)}...`, 'info');
                    await checkSessionStatus();
                } else {
                    log(`❌ Failed to lock session: ${response.error?.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Lock session error: ${error.message}`, 'error');
            }
        }

        async function unlockSession(method) {
            try {
                if (!currentLockToken) {
                    log('❌ No lock token available for unlock', 'error');
                    return;
                }

                const credentials = {};
                if (method === 'pin') {
                    const pin = document.getElementById('pinInput').value;
                    if (!pin) {
                        log('❌ Please enter a PIN', 'error');
                        return;
                    }
                    credentials.pin = pin;
                }

                log(`Attempting to unlock session with method: ${method}`);
                const response = await makeApiCall('POST', '/auth/session/unlock', {
                    lockToken: currentLockToken,
                    method,
                    credentials
                });
                
                if (response.success) {
                    updateLockToken(null);
                    log(`✅ Session unlocked successfully! Method: ${method}`, 'success');
                    await checkSessionStatus();
                } else {
                    log(`❌ Failed to unlock session: ${response.error?.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Unlock session error: ${error.message}`, 'error');
            }
        }

        async function checkSessionStatus() {
            try {
                log('Checking session status...');
                const response = await makeApiCall('GET', '/auth/session/status');
                
                if (response.success) {
                    const statusDiv = document.getElementById('sessionStatus');
                    if (response.data.isLocked) {
                        statusDiv.textContent = `Session Locked 🔒 (${response.data.lockData?.reason || 'unknown reason'})`;
                        statusDiv.className = 'status warning';
                        log(`🔒 Session is locked. Reason: ${response.data.lockData?.reason}`, 'warning');
                    } else {
                        statusDiv.textContent = 'Session Unlocked 🔓';
                        statusDiv.className = 'status success';
                        log('🔓 Session is unlocked', 'success');
                    }
                } else {
                    log(`❌ Failed to check session status: ${response.error?.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Session status error: ${error.message}`, 'error');
            }
        }

        // Security attack tests
        async function tryLocalStorageBypass() {
            log('🚨 ATTACK TEST: Trying localStorage bypass...', 'warning');
            
            // Try to manipulate localStorage
            localStorage.setItem('session_lock_required', 'false');
            localStorage.setItem('session_locked', 'false');
            localStorage.setItem('lock_bypassed', 'true');
            
            log('📝 Set localStorage flags to bypass lock', 'info');
            
            // Check if session is still locked
            await checkSessionStatus();
            log('🔍 Result: localStorage manipulation has no effect on server-side lock', 'success');
        }

        async function tryTokenForging() {
            log('🚨 ATTACK TEST: Trying token forging...', 'warning');
            
            const fakeToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmYWtlIjoidG9rZW4ifQ.fake-signature';
            
            try {
                const response = await makeApiCall('POST', '/auth/session/unlock', {
                    lockToken: fakeToken,
                    method: 'pin',
                    credentials: { pin: '123456' }
                });
                log('❌ SECURITY BREACH: Fake token was accepted!', 'error');
            } catch (error) {
                log('✅ SECURITY OK: Fake token was rejected', 'success');
            }
        }

        async function tryDirectUnlock() {
            log('🚨 ATTACK TEST: Trying direct unlock without token...', 'warning');
            
            try {
                const response = await makeApiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    credentials: { pin: '123456' }
                });
                log('❌ SECURITY BREACH: Unlock without token was accepted!', 'error');
            } catch (error) {
                log('✅ SECURITY OK: Unlock without token was rejected', 'success');
            }
        }

        async function tryExpiredToken() {
            log('🚨 ATTACK TEST: Trying expired token...', 'warning');
            
            // This would need a real expired token to test properly
            log('⚠️ This test requires a real expired token', 'warning');
        }

        async function runSecurityTests() {
            log('🔒 Running comprehensive security tests...', 'info');
            
            await tryLocalStorageBypass();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await tryTokenForging();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await tryDirectUnlock();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            log('✅ All security tests completed', 'success');
            
            const resultsDiv = document.getElementById('securityResults');
            resultsDiv.textContent = 'Security tests passed ✅ - Session lock is secure';
            resultsDiv.className = 'status success';
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateAuthStatus();
            checkSessionStatus();
            log('🚀 Secure Session Lock Test initialized');
            
            if (!authToken) {
                log('⚠️ No auth token found. Please login first.', 'warning');
            }
        });
    </script>
</body>
</html>
