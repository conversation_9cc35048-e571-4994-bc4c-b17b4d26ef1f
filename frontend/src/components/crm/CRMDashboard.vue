<template>
  <div class="crm-dashboard p-6 space-y-6">
    <!-- Dashboard Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-base-content">CRM Dashboard</h1>
        <p class="text-base-content/70 mt-1">
          Complete customer relationship management for HLenergy
        </p>
      </div>
      
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <button @click="refreshData" :disabled="isLoading" class="btn btn-primary">
          <span v-if="!isLoading">Refresh</span>
          <span v-else class="loading loading-spinner loading-sm"></span>
        </button>
        <RouterLink to="/crm/customers/new" class="btn btn-accent">
          + New Customer
        </RouterLink>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      <!-- Total Customers -->
      <div class="card bg-gradient-to-br from-[#02342b] to-[#12816c] text-white shadow-xl border border-[#02342b]/20">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Total Customers</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ totalCustomers.toLocaleString() }}</div>
              <div class="text-xs sm:text-sm opacity-80">{{ activeCustomers.length }} active</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="users" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- Active Projects -->
      <div class="card bg-gradient-to-br from-[#eaaa34] to-[#7fbf60] text-white shadow-xl border border-[#eaaa34]/20">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Active Projects</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ activeProjects.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">{{ totalProjects }} total</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="document" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- High Priority Leads -->
      <div class="card bg-gradient-to-br from-[#5cad64] to-[#389868] text-white shadow-xl border border-[#5cad64]/20">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">High Priority</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ highPriorityCustomers.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">Urgent attention needed</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="warning" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- Unread Communications -->
      <div class="card bg-gradient-to-br from-[#12816c] to-[#02342b] text-white shadow-xl border border-[#12816c]/20">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Unread Messages</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ unreadCommunications.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">Require response</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="email" size="xl" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
      <!-- Recent Customers -->
      <div class="lg:col-span-2">
        <div class="card bg-base-100 shadow-xl border border-base-300/50">
          <div class="card-body p-4 sm:p-6">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
              <h3 class="card-title text-lg sm:text-xl">Recent Customers</h3>
              <RouterLink to="/crm/customers" class="btn btn-sm btn-ghost self-start sm:self-auto">View All</RouterLink>
            </div>

            <!-- Mobile Card Layout -->
            <div class="block sm:hidden space-y-3">
              <div v-for="customer in customers.slice(0, 5)" :key="customer.id" class="card bg-base-200 p-4">
                <div class="flex items-start justify-between mb-2">
                  <div class="flex-1">
                    <div class="font-medium text-base">{{ customer.firstName }} {{ customer.lastName }}</div>
                    <div class="text-sm opacity-70">{{ customer.email }}</div>
                    <div class="text-sm text-base-content opacity-60 mt-1">{{ customer.companyName || 'No company' }}</div>
                  </div>
                  <div class="badge" :class="getStatusBadgeClass(customer.status)">
                    {{ customer.status }}
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-16 bg-base-300 rounded-full h-2">
                      <div
                        class="bg-primary h-2 rounded-full"
                        :style="{ width: `${customer.leadScore}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-medium">{{ customer.leadScore }}</span>
                  </div>
                  <RouterLink
                    :to="`/crm/customers/${customer.id}`"
                    class="btn btn-xs btn-primary"
                  >
                    View
                  </RouterLink>
                </div>
              </div>
            </div>

            <!-- Desktop Table Layout -->
            <div class="hidden sm:block overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th class="text-sm">Name</th>
                    <th class="text-sm">Company</th>
                    <th class="text-sm">Status</th>
                    <th class="text-sm">Score</th>
                    <th class="text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="customer in customers.slice(0, 5)" :key="customer.id">
                    <td>
                      <div class="font-medium">{{ customer.firstName }} {{ customer.lastName }}</div>
                      <div class="text-sm opacity-70">{{ customer.email }}</div>
                    </td>
                    <td>{{ customer.companyName || 'N/A' }}</td>
                    <td>
                      <div class="badge" :class="getStatusBadgeClass(customer.status)">
                        {{ customer.status }}
                      </div>
                    </td>
                    <td>
                      <div class="flex items-center space-x-2">
                        <div class="w-12 bg-base-200 rounded-full h-2">
                          <div
                            class="bg-primary h-2 rounded-full"
                            :style="{ width: `${customer.leadScore}%` }"
                          ></div>
                        </div>
                        <span class="text-sm">{{ customer.leadScore }}</span>
                      </div>
                    </td>
                    <td>
                      <RouterLink
                        :to="`/crm/customers/${customer.id}`"
                        class="btn btn-sm btn-ghost"
                      >
                        View
                      </RouterLink>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions & Stats -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="card bg-base-100 shadow-xl border border-base-300/50">
          <div class="card-body">
            <h3 class="card-title mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <RouterLink to="/crm/customers/new" class="btn btn-block btn-primary">
                + Add New Customer
              </RouterLink>
              <RouterLink to="/crm/projects/new" class="btn btn-block btn-secondary">
                + Create Project
              </RouterLink>
              <RouterLink to="/crm/communications/new" class="btn btn-block btn-accent">
                + Log Communication
              </RouterLink>
              <RouterLink to="/crm/documents" class="btn btn-block btn-info">
                📁 Manage Documents
              </RouterLink>
            </div>
          </div>
        </div>

        <!-- Lead Sources -->
        <div class="card bg-base-100 shadow-xl border border-base-300/50">
          <div class="card-body">
            <h3 class="card-title mb-4">Lead Sources</h3>
            <div class="space-y-3">
              <div v-for="source in leadSources" :key="source.name" class="flex items-center justify-between">
                <span class="font-medium">{{ source.name }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-base-200 rounded-full h-2">
                    <div 
                      class="bg-primary h-2 rounded-full"
                      :style="{ width: `${source.percentage}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-bold">{{ source.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Overdue Projects -->
        <div v-if="overdueProjects.length > 0" class="card bg-base-100 shadow-xl border border-base-300/50 border-l-4 border-l-error">
          <div class="card-body">
            <h3 class="card-title text-error mb-4">⚠️ Overdue Projects</h3>
            <div class="space-y-2">
              <div v-for="project in overdueProjects.slice(0, 3)" :key="project.id" class="p-3 bg-error/10 rounded-lg">
                <div class="font-medium">{{ project.title }}</div>
                <div class="text-sm text-error">Due: {{ formatDate(project.endDate) }}</div>
              </div>
            </div>
            <div class="card-actions justify-end mt-4">
              <RouterLink to="/crm/projects?filter=overdue" class="btn btn-sm btn-error">
                View All Overdue
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card bg-base-100 shadow-xl border border-base-300/50">
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title">Recent Activity</h3>
          <RouterLink to="/crm/communications" class="btn btn-sm btn-ghost">View All</RouterLink>
        </div>
        
        <div class="space-y-4">
          <div v-for="comm in communications.slice(0, 5)" :key="comm.id" class="flex items-start space-x-4 p-4 bg-base-200/50 hover:bg-base-200 rounded-lg transition-colors">
            <div class="avatar placeholder">
              <div class="bg-gradient-to-br from-[#02342b] to-[#12816c] text-white rounded-full w-10">
                <span class="text-sm">{{ getActivityIcon(comm.type) }}</span>
              </div>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <div class="font-medium">{{ comm.subject || `${comm.type} with ${comm.customer?.firstName} ${comm.customer?.lastName}` }}</div>
                <div class="text-sm text-base-content/70">{{ formatRelativeTime(comm.createdAt) }}</div>
              </div>
              <div class="text-sm text-base-content/70 mt-1">{{ comm.summary || comm.content?.substring(0, 100) + '...' }}</div>
              <div class="flex items-center space-x-2 mt-2">
                <div class="badge badge-sm" :class="getOutcomeBadgeClass(comm.outcome)">
                  {{ comm.outcome || comm.status }}
                </div>
                <div v-if="comm.followUpRequired" class="badge badge-warning badge-sm">
                  Follow-up needed
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useCRMStore } from '@/stores/crm'
import Icon from '@/components/common/Icon.vue'

const crmStore = useCRMStore()

const {
  customers,
  projects,
  communications,
  isLoading,
  totalCustomers,
  totalProjects,
  activeCustomers,
  activeProjects,
  highPriorityCustomers,
  overdueProjects,
  unreadCommunications,
  fetchCustomers,
  fetchProjects,
  fetchCommunications,
  fetchDashboardData
} = crmStore

// Mock data for lead sources (in production, this would come from the store)
const leadSources = computed(() => [
  { name: 'Website', count: 45, percentage: 90 },
  { name: 'Referral', count: 32, percentage: 64 },
  { name: 'Social Media', count: 28, percentage: 56 },
  { name: 'Email Campaign', count: 15, percentage: 30 },
  { name: 'Cold Outreach', count: 12, percentage: 24 }
])

const refreshData = async () => {
  await Promise.all([
    fetchCustomers({ limit: 10 }),
    fetchProjects({ limit: 10 }),
    fetchCommunications({ limit: 10 }),
    fetchDashboardData()
  ])
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    lead: 'badge-info',
    prospect: 'badge-warning',
    customer: 'badge-success',
    inactive: 'badge-ghost'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getOutcomeBadgeClass = (outcome?: string) => {
  const classes = {
    successful: 'badge-success',
    follow_up_needed: 'badge-warning',
    not_interested: 'badge-error',
    meeting_scheduled: 'badge-info'
  }
  return classes[outcome as keyof typeof classes] || 'badge-ghost'
}

const getActivityIcon = (type: string) => {
  const icons = {
    email: 'email',
    phone_call: 'phone',
    meeting: 'users',
    video_call: 'video',
    note: 'document-text',
    task: 'check'
  }
  return icons[type as keyof typeof icons] || 'chat'
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
  return date.toLocaleDateString()
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.crm-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
</style>
