<template>
  <div class="crm-dashboard p-6 space-y-6">
    <!-- Dashboard Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-base-content">CRM Dashboard</h1>
        <p class="text-base-content/70 mt-1">
          Complete customer relationship management for HLenergy
        </p>
      </div>
      
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <button @click="refreshData" :disabled="isLoading" class="btn btn-primary">
          <span v-if="!isLoading">Refresh</span>
          <span v-else class="loading loading-spinner loading-sm"></span>
        </button>
        <RouterLink to="/crm/customers/new" class="btn btn-accent">
          + New Customer
        </RouterLink>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      <!-- Total Customers -->
      <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-105 bg-gradient-to-br from-[#02342b]/80 to-[#12816c]/80 text-white">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Total Customers</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ totalCustomers.toLocaleString() }}</div>
              <div class="text-xs sm:text-sm opacity-80">{{ activeCustomers.length }} active</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="users" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- Active Projects -->
      <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-105 bg-gradient-to-br from-[#eaaa34]/80 to-[#7fbf60]/80 text-white">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Active Projects</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ activeProjects.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">{{ totalProjects }} total</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="document" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- High Priority Leads -->
      <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-105 bg-gradient-to-br from-[#5cad64]/80 to-[#389868]/80 text-white">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">High Priority</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ highPriorityCustomers.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">Urgent attention needed</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="warning" size="xl" />
            </div>
          </div>
        </div>
      </div>

      <!-- Unread Communications -->
      <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-105 bg-gradient-to-br from-[#12816c]/80 to-[#02342b]/80 text-white">
        <div class="card-body p-4 sm:p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h3 class="text-sm sm:text-lg font-semibold opacity-90">Unread Messages</h3>
              <div class="text-2xl sm:text-3xl font-bold">{{ unreadCommunications.length }}</div>
              <div class="text-xs sm:text-sm opacity-80">Require response</div>
            </div>
            <div class="text-2xl sm:text-4xl opacity-60">
              <Icon name="email" size="xl" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
      <!-- Recent Customers -->
      <div class="lg:col-span-2">
        <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/10 transition-all duration-300 hover:scale-[1.02] bg-base-100/70">
          <div class="card-body p-4 sm:p-6">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
              <h3 class="card-title text-lg sm:text-xl">Recent Customers</h3>
              <RouterLink to="/crm/customers" class="btn btn-sm btn-ghost self-start sm:self-auto">View All</RouterLink>
            </div>

            <!-- Mobile Card Layout -->
            <div class="block sm:hidden space-y-3">
              <div v-for="customer in customers.slice(0, 5)" :key="customer.id" class="card bg-base-200 p-4">
                <div class="flex items-start justify-between mb-2">
                  <div class="flex-1">
                    <div class="font-medium text-base">{{ customer.firstName }} {{ customer.lastName }}</div>
                    <div class="text-sm opacity-70">{{ customer.email }}</div>
                    <div class="text-sm text-base-content opacity-60 mt-1">{{ customer.companyName || 'No company' }}</div>
                  </div>
                  <div class="badge" :class="getStatusBadgeClass(customer.status)">
                    {{ customer.status }}
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-16 bg-base-300 rounded-full h-2">
                      <div
                        class="bg-primary h-2 rounded-full"
                        :style="{ width: `${customer.leadScore}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-medium">{{ customer.leadScore }}</span>
                  </div>
                  <RouterLink
                    :to="`/crm/customers/${customer.id}`"
                    class="btn btn-xs btn-primary"
                  >
                    View
                  </RouterLink>
                </div>
              </div>
            </div>

            <!-- Desktop Table Layout -->
            <div class="hidden sm:block overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th class="text-sm">Name</th>
                    <th class="text-sm">Company</th>
                    <th class="text-sm">Status</th>
                    <th class="text-sm">Score</th>
                    <th class="text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="customer in customers.slice(0, 5)" :key="customer.id">
                    <td>
                      <div class="font-medium">{{ customer.firstName }} {{ customer.lastName }}</div>
                      <div class="text-sm opacity-70">{{ customer.email }}</div>
                    </td>
                    <td>{{ customer.companyName || 'N/A' }}</td>
                    <td>
                      <div class="badge" :class="getStatusBadgeClass(customer.status)">
                        {{ customer.status }}
                      </div>
                    </td>
                    <td>
                      <div class="flex items-center space-x-2">
                        <div class="w-12 bg-base-200 rounded-full h-2">
                          <div
                            class="bg-primary h-2 rounded-full"
                            :style="{ width: `${customer.leadScore}%` }"
                          ></div>
                        </div>
                        <span class="text-sm">{{ customer.leadScore }}</span>
                      </div>
                    </td>
                    <td>
                      <RouterLink
                        :to="`/crm/customers/${customer.id}`"
                        class="btn btn-sm btn-ghost"
                      >
                        View
                      </RouterLink>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions & Stats -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/10 transition-all duration-300 hover:scale-[1.02] bg-base-100/70">
          <div class="card-body">
            <h3 class="card-title mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <button @click="openNewCustomerModal" class="btn btn-block btn-primary">
                <Icon name="plus" size="sm" class="mr-2" />
                Add New Customer
              </button>
              <button @click="openNewProjectModal" class="btn btn-block btn-secondary">
                <Icon name="document" size="sm" class="mr-2" />
                Create Project
              </button>
              <button @click="openCommunicationModal" class="btn btn-block btn-accent">
                <Icon name="chat" size="sm" class="mr-2" />
                Log Communication
              </button>
              <button @click="openDocumentManager" class="btn btn-block btn-info">
                <Icon name="folder" size="sm" class="mr-2" />
                Manage Documents
              </button>
            </div>
          </div>
        </div>

        <!-- Lead Sources -->
        <div class="card shadow-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-[1.02] bg-gradient-to-br from-base-100/10 to-base-100/5 border-base-300/20">
          <div class="card-body">
            <h3 class="card-title mb-4">Lead Sources</h3>
            <div class="space-y-3">
              <div v-for="source in leadSources" :key="source.name" class="flex items-center justify-between">
                <span class="font-medium">{{ source.name }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-base-200 rounded-full h-2">
                    <div 
                      class="bg-primary h-2 rounded-full"
                      :style="{ width: `${source.percentage}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-bold">{{ source.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Overdue Projects -->
        <div v-if="overdueProjects.length > 0" class="card shadow-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-[1.02] bg-gradient-to-br from-error/10 to-error/5 border-error/20 border-l-4 border-l-error">
          <div class="card-body">
            <h3 class="card-title text-error mb-4">⚠️ Overdue Projects</h3>
            <div class="space-y-2">
              <div v-for="project in overdueProjects.slice(0, 3)" :key="project.id" class="p-3 bg-error/10 rounded-lg">
                <div class="font-medium">{{ project.title }}</div>
                <div class="text-sm text-error">Due: {{ formatDate(project.endDate) }}</div>
              </div>
            </div>
            <div class="card-actions justify-end mt-4">
              <RouterLink to="/crm/projects?filter=overdue" class="btn btn-sm btn-error">
                View All Overdue
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card shadow-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-[1.02] bg-gradient-to-br from-base-100/10 to-base-100/5 border-base-300/20">
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title">Recent Activity</h3>
          <RouterLink to="/crm/communications" class="btn btn-sm btn-ghost">View All</RouterLink>
        </div>
        
        <div class="space-y-4">
          <div v-for="comm in communications.slice(0, 5)" :key="comm.id" class="flex items-start space-x-4 p-4 bg-base-200/50 hover:bg-base-200 rounded-lg transition-colors">
            <div class="avatar placeholder">
              <div class="bg-gradient-to-br from-[#02342b] to-[#12816c] text-white rounded-full w-10">
                <span class="text-sm">{{ getActivityIcon(comm.type) }}</span>
              </div>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <div class="font-medium">{{ comm.subject || `${comm.type} with ${comm.customer?.firstName} ${comm.customer?.lastName}` }}</div>
                <div class="text-sm text-base-content/70">{{ formatRelativeTime(comm.createdAt) }}</div>
              </div>
              <div class="text-sm text-base-content/70 mt-1">{{ comm.summary || comm.content?.substring(0, 100) + '...' }}</div>
              <div class="flex items-center space-x-2 mt-2">
                <div class="badge badge-sm" :class="getOutcomeBadgeClass(comm.outcome)">
                  {{ comm.outcome || comm.status }}
                </div>
                <div v-if="comm.followUpRequired" class="badge badge-warning badge-sm">
                  Follow-up needed
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- New Customer Modal -->
  <div v-if="showNewCustomerModal" class="modal modal-open">
    <div class="modal-box glass-effect bg-base-100/90 backdrop-blur-md border border-white/20">
      <h3 class="font-bold text-lg mb-4">Add New Customer</h3>
      <form @submit.prevent="createCustomer" class="space-y-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">Company Name *</span>
          </label>
          <input v-model="newCustomer.name" type="text" class="input input-bordered" required />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Email *</span>
          </label>
          <input v-model="newCustomer.email" type="email" class="input input-bordered" required />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Phone</span>
          </label>
          <input v-model="newCustomer.phone" type="tel" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Industry</span>
          </label>
          <select v-model="newCustomer.industry" class="select select-bordered">
            <option value="">Select Industry</option>
            <option value="Technology">Technology</option>
            <option value="Healthcare">Healthcare</option>
            <option value="Finance">Finance</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Retail">Retail</option>
            <option value="Other">Other</option>
          </select>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Priority</span>
          </label>
          <select v-model="newCustomer.priority" class="select select-bordered">
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
        </div>
        <div class="modal-action">
          <button type="button" @click="closeNewCustomerModal" class="btn btn-ghost">Cancel</button>
          <button type="submit" class="btn btn-primary" :disabled="isCreatingCustomer">
            <span v-if="isCreatingCustomer" class="loading loading-spinner loading-sm"></span>
            {{ isCreatingCustomer ? 'Creating...' : 'Create Customer' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- New Project Modal -->
  <div v-if="showNewProjectModal" class="modal modal-open">
    <div class="modal-box glass-effect bg-base-100/90 backdrop-blur-md border border-white/20">
      <h3 class="font-bold text-lg mb-4">Create New Project</h3>
      <form @submit.prevent="createProject" class="space-y-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">Project Name *</span>
          </label>
          <input v-model="newProject.name" type="text" class="input input-bordered" required />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Customer *</span>
          </label>
          <select v-model="newProject.customerId" class="select select-bordered" required>
            <option value="">Select Customer</option>
            <option v-for="customer in customers" :key="customer.id" :value="customer.id">
              {{ customer.name }}
            </option>
          </select>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Description</span>
          </label>
          <textarea v-model="newProject.description" class="textarea textarea-bordered" rows="3"></textarea>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Start Date</span>
          </label>
          <input v-model="newProject.startDate" type="date" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">End Date</span>
          </label>
          <input v-model="newProject.endDate" type="date" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text">Status</span>
          </label>
          <select v-model="newProject.status" class="select select-bordered">
            <option value="planning">Planning</option>
            <option value="active">Active</option>
            <option value="on-hold">On Hold</option>
            <option value="completed">Completed</option>
          </select>
        </div>
        <div class="modal-action">
          <button type="button" @click="closeNewProjectModal" class="btn btn-ghost">Cancel</button>
          <button type="submit" class="btn btn-primary" :disabled="isCreatingProject">
            <span v-if="isCreatingProject" class="loading loading-spinner loading-sm"></span>
            {{ isCreatingProject ? 'Creating...' : 'Create Project' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Document Manager Modal -->
  <div v-if="showDocumentManager" class="modal modal-open">
    <div class="modal-box max-w-4xl glass-effect bg-base-100/90 backdrop-blur-md border border-white/20">
      <h3 class="font-bold text-lg mb-4">Document Manager</h3>

      <!-- Upload Section -->
      <div class="mb-6 p-4 border-2 border-dashed border-base-300 rounded-lg">
        <div class="text-center">
          <Icon name="cloud-upload" size="xl" class="mx-auto mb-2 text-base-content/50" />
          <p class="mb-2">Drag and drop files here or click to browse</p>
          <input
            ref="fileInput"
            type="file"
            multiple
            @change="handleFileUpload"
            class="file-input file-input-bordered w-full max-w-xs"
          />
        </div>
      </div>

      <!-- Documents List -->
      <div class="space-y-2 max-h-96 overflow-y-auto">
        <div v-for="doc in documents" :key="doc.id" class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
          <div class="flex items-center space-x-3">
            <Icon :name="getFileIcon(doc.type)" size="sm" />
            <div>
              <p class="font-medium">{{ doc.name }}</p>
              <p class="text-sm text-base-content/70">{{ formatFileSize(doc.size) }} • {{ formatDate(doc.uploadedAt) }}</p>
            </div>
          </div>
          <div class="flex space-x-2">
            <button @click="downloadDocument(doc)" class="btn btn-sm btn-ghost">
              <Icon name="download" size="sm" />
            </button>
            <button @click="deleteDocument(doc.id)" class="btn btn-sm btn-error">
              <Icon name="trash" size="sm" />
            </button>
          </div>
        </div>
        <div v-if="documents.length === 0" class="text-center py-8 text-base-content/50">
          No documents uploaded yet
        </div>
      </div>

      <div class="modal-action">
        <button @click="closeDocumentManager" class="btn btn-ghost">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import { useCRMStore } from '@/stores/crm'
import { useCommunicationModal } from '@/composables/useCommunicationModal'
import Icon from '@/components/common/Icon.vue'

const crmStore = useCRMStore()
const { openModal: openCommunicationModal } = useCommunicationModal()

const {
  customers,
  projects,
  communications,
  isLoading,
  totalCustomers,
  totalProjects,
  activeCustomers,
  activeProjects,
  highPriorityCustomers,
  overdueProjects,
  unreadCommunications,
  fetchCustomers,
  fetchProjects,
  fetchCommunications,
  fetchDashboardData
} = crmStore

// Mock data for lead sources (in production, this would come from the store)
const leadSources = computed(() => [
  { name: 'Website', count: 45, percentage: 90 },
  { name: 'Referral', count: 32, percentage: 64 },
  { name: 'Social Media', count: 28, percentage: 56 },
  { name: 'Email Campaign', count: 15, percentage: 30 },
  { name: 'Cold Outreach', count: 12, percentage: 24 }
])

const refreshData = async () => {
  await Promise.all([
    fetchCustomers({ limit: 10 }),
    fetchProjects({ limit: 10 }),
    fetchCommunications({ limit: 10 }),
    fetchDashboardData()
  ])
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    lead: 'badge-info',
    prospect: 'badge-warning',
    customer: 'badge-success',
    inactive: 'badge-ghost'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getOutcomeBadgeClass = (outcome?: string) => {
  const classes = {
    successful: 'badge-success',
    follow_up_needed: 'badge-warning',
    not_interested: 'badge-error',
    meeting_scheduled: 'badge-info'
  }
  return classes[outcome as keyof typeof classes] || 'badge-ghost'
}

const getActivityIcon = (type: string) => {
  const icons = {
    email: 'email',
    phone_call: 'phone',
    meeting: 'users',
    video_call: 'video',
    note: 'document-text',
    task: 'check'
  }
  return icons[type as keyof typeof icons] || 'chat'
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
  return date.toLocaleDateString()
}

// Modal states
const showNewCustomerModal = ref(false)
const showNewProjectModal = ref(false)
const showDocumentManager = ref(false)

// Loading states
const isCreatingCustomer = ref(false)
const isCreatingProject = ref(false)

// Form data
const newCustomer = ref({
  name: '',
  email: '',
  phone: '',
  industry: '',
  priority: 'medium'
})

const newProject = ref({
  name: '',
  customerId: '',
  description: '',
  startDate: '',
  endDate: '',
  status: 'planning'
})

// Documents
const documents = ref([
  {
    id: 1,
    name: 'Project Proposal.pdf',
    type: 'pdf',
    size: 2048576,
    uploadedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'Contract.docx',
    type: 'docx',
    size: 1024000,
    uploadedAt: '2024-01-14T14:20:00Z'
  }
])

const fileInput = ref(null)

// Modal functions
const openNewCustomerModal = () => {
  showNewCustomerModal.value = true
}

const closeNewCustomerModal = () => {
  showNewCustomerModal.value = false
  newCustomer.value = {
    name: '',
    email: '',
    phone: '',
    industry: '',
    priority: 'medium'
  }
}

const openNewProjectModal = () => {
  showNewProjectModal.value = true
}

const closeNewProjectModal = () => {
  showNewProjectModal.value = false
  newProject.value = {
    name: '',
    customerId: '',
    description: '',
    startDate: '',
    endDate: '',
    status: 'planning'
  }
}

const openDocumentManager = () => {
  showDocumentManager.value = true
}

const closeDocumentManager = () => {
  showDocumentManager.value = false
}

// Create customer
const createCustomer = async () => {
  try {
    isCreatingCustomer.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    const customer = {
      id: customers.value.length + 1,
      name: newCustomer.value.name,
      email: newCustomer.value.email,
      phone: newCustomer.value.phone,
      industry: newCustomer.value.industry,
      priority: newCustomer.value.priority,
      status: 'active',
      createdAt: new Date().toISOString(),
      lastContact: new Date().toISOString()
    }

    customers.value.push(customer)
    closeNewCustomerModal()

    // Show success message
    alert('Customer created successfully!')

  } catch (error) {
    console.error('Error creating customer:', error)
    alert('Failed to create customer. Please try again.')
  } finally {
    isCreatingCustomer.value = false
  }
}

// Create project
const createProject = async () => {
  try {
    isCreatingProject.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    const project = {
      id: projects.value.length + 1,
      name: newProject.value.name,
      customerId: parseInt(newProject.value.customerId),
      description: newProject.value.description,
      startDate: newProject.value.startDate,
      endDate: newProject.value.endDate,
      status: newProject.value.status,
      createdAt: new Date().toISOString()
    }

    projects.value.push(project)
    closeNewProjectModal()

    // Show success message
    alert('Project created successfully!')

  } catch (error) {
    console.error('Error creating project:', error)
    alert('Failed to create project. Please try again.')
  } finally {
    isCreatingProject.value = false
  }
}

// Document functions
const handleFileUpload = (event) => {
  const files = event.target.files
  if (files) {
    Array.from(files).forEach(file => {
      const newDoc = {
        id: documents.value.length + 1,
        name: file.name,
        type: file.name.split('.').pop().toLowerCase(),
        size: file.size,
        uploadedAt: new Date().toISOString()
      }
      documents.value.push(newDoc)
    })

    // Clear the input
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const getFileIcon = (type) => {
  switch (type) {
    case 'pdf': return 'document'
    case 'doc':
    case 'docx': return 'document-text'
    case 'xls':
    case 'xlsx': return 'table'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif': return 'photo'
    default: return 'document'
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const downloadDocument = (doc) => {
  // Simulate download
  alert(`Downloading ${doc.name}...`)
}

const deleteDocument = (docId) => {
  if (confirm('Are you sure you want to delete this document?')) {
    documents.value = documents.value.filter(doc => doc.id !== docId)
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.crm-dashboard {
  min-height: 100vh;
  background: oklch(var(--b2));
}

/* Proper glass effect */
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-effect:hover::after {
  opacity: 1;
}

.glass-effect .card-body {
  position: relative;
  z-index: 3;
}

/* Enhanced hover effects */
.glass-effect:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* Dark theme adjustments */
[data-theme="hlenergy-dark"] .glass-effect::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 100%
  );
}

[data-theme="hlenergy-dark"] .glass-effect:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}
</style>
