<template>
  <div v-if="authStore.isLocked" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-base-100 rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <div class="text-center mb-6">
        <Icon name="lock" size="lg" class="text-warning mx-auto mb-4" />
        <h2 class="text-2xl font-bold mb-2">{{ $t('auth.session_locked') }}</h2>
        <p class="text-base-content/70">{{ $t('auth.session_locked_desc') }}</p>
      </div>

      <!-- Authentication Methods -->
      <div class="tabs tabs-boxed mb-6">
        <button 
          @click="authMethod = 'pin'"
          :class="['tab', { 'tab-active': authMethod === 'pin' }]"
        >
          <Icon name="hashtag" size="sm" class="mr-2" />
          PIN
        </button>
        <button 
          @click="authMethod = 'biometric'"
          :class="['tab', { 'tab-active': authMethod === 'biometric' }]"
          :disabled="!biometricAvailable"
        >
          <Icon name="fingerprint" size="sm" class="mr-2" />
          Biometric
        </button>
      </div>

      <!-- PIN Authentication -->
      <div v-if="authMethod === 'pin'" class="space-y-4">
        <div class="grid grid-cols-4 gap-2 max-w-xs mx-auto">
          <input
            v-for="(digit, index) in pinDigits"
            :key="index"
            v-model="pinDigits[index]"
            type="password"
            maxlength="1"
            class="input input-bordered text-center text-lg font-mono"
            :class="{ 'input-error': pinError }"
            @input="handlePinInput(index, $event)"
            @keydown="handlePinKeydown(index, $event)"
            :ref="el => pinInputs[index] = el"
          />
        </div>
        
        <div v-if="pinError" class="alert alert-error">
          <Icon name="exclamation-triangle" size="sm" />
          <span>{{ pinError }}</span>
        </div>

        <button
          @click="handlePinUnlock"
          class="btn btn-primary w-full"
          :disabled="isLoading || pinDigits.some(d => !d)"
        >
          <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
          <Icon v-if="!isLoading" name="unlock" size="sm" class="mr-2" />
          {{ isLoading ? $t('auth.verifying') : $t('auth.unlock_session') }}
        </button>
      </div>

      <!-- Biometric Authentication -->
      <div v-if="authMethod === 'biometric'" class="space-y-4">
        <div class="text-center py-8">
          <Icon name="fingerprint" size="xl" class="text-primary mx-auto mb-4" />
          <p class="text-lg mb-4">{{ $t('auth.biometric_prompt') }}</p>
        </div>

        <button
          @click="handleBiometricUnlock"
          class="btn btn-primary w-full"
          :disabled="isLoading || !biometricAvailable"
        >
          <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
          <Icon v-if="!isLoading" name="fingerprint" size="sm" class="mr-2" />
          {{ isLoading ? $t('auth.scanning') : $t('auth.authenticate') }}
        </button>
      </div>

      <!-- Logout Option -->
      <div class="divider">{{ $t('common.or') }}</div>
      <button
        @click="handleLogout"
        class="btn btn-outline btn-error w-full"
        :disabled="isLoading"
      >
        <Icon name="logout" size="sm" class="mr-2" />
        {{ $t('auth.logout') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import Icon from '@/components/common/Icon.vue'

const authStore = useAuthStore()
const router = useRouter()

// State
const authMethod = ref<'pin' | 'biometric'>('pin')
const isLoading = ref(false)
const biometricAvailable = ref(false)

// PIN state
const pinDigits = ref(['', '', '', ''])
const pinInputs = ref<HTMLInputElement[]>([])
const pinError = ref('')

// Methods
const handlePinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  if (value && /^\d$/.test(value)) {
    pinDigits.value[index] = value
    
    // Move to next input
    if (index < 3) {
      nextTick(() => {
        pinInputs.value[index + 1]?.focus()
      })
    }
  } else {
    target.value = ''
    pinDigits.value[index] = ''
  }
  
  clearPinError()
}

const handlePinKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !pinDigits.value[index] && index > 0) {
    nextTick(() => {
      pinInputs.value[index - 1]?.focus()
    })
  }
}

const clearPinError = () => {
  pinError.value = ''
}

const handlePinUnlock = async () => {
  try {
    isLoading.value = true
    clearPinError()
    
    const pin = pinDigits.value.join('')
    if (pin.length !== 4) {
      pinError.value = 'Please enter a 4-digit PIN'
      return
    }
    
    const success = await authStore.unlockSession('pin', { pin })
    if (!success) {
      pinError.value = 'Invalid PIN. Please try again.'
      pinDigits.value = ['', '', '', '']
      nextTick(() => {
        pinInputs.value[0]?.focus()
      })
    }
  } catch (error: any) {
    pinError.value = error.message || 'PIN verification failed'
  } finally {
    isLoading.value = false
  }
}

const handleBiometricUnlock = async () => {
  try {
    isLoading.value = true
    
    const success = await authStore.unlockSession('biometric')
    if (!success) {
      // Error handling is done in the store
    }
  } catch (error) {
    console.error('Biometric unlock failed:', error)
  } finally {
    isLoading.value = false
  }
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

const checkBiometricAvailability = async () => {
  try {
    biometricAvailable.value = !!(window.navigator.credentials && 
      await window.navigator.credentials.get({ publicKey: { challenge: new Uint8Array(32) } }))
  } catch {
    biometricAvailable.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await checkBiometricAvailability()
  
  // Focus first PIN input
  nextTick(() => {
    pinInputs.value[0]?.focus()
  })
})
</script>
