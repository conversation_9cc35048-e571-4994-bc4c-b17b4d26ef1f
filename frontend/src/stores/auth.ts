import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authService, type User, type LoginRequest, type RegisterRequest } from '@/services/auth'
import { biometricService } from '@/services/biometric'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Idle timeout state
  const isLocked = ref(false)
  const lastActivity = ref<Date>(new Date())
  const idleTimeout = ref<NodeJS.Timeout | null>(null)
  const IDLE_TIMEOUT_MINUTES = 5

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value && !isLocked.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isStaff = computed(() => user.value?.role === 'staff' || isAdmin.value)
  const isEmailVerified = computed(() => user.value?.email_verified || false)
  const userInitials = computed(() => {
    if (!user.value) return ''
    const names = user.value.name.split(' ')
    return names.map(name => name.charAt(0).toUpperCase()).join('')
  })

  // Idle timeout functions
  const updateActivity = () => {
    lastActivity.value = new Date()
    resetIdleTimeout()
  }

  const resetIdleTimeout = () => {
    if (idleTimeout.value) {
      clearTimeout(idleTimeout.value)
    }

    if (isAuthenticated.value && !isLocked.value) {
      idleTimeout.value = setTimeout(() => {
        lockSession()
      }, IDLE_TIMEOUT_MINUTES * 60 * 1000)
    }
  }

  const lockSession = () => {
    isLocked.value = true
    if (idleTimeout.value) {
      clearTimeout(idleTimeout.value)
      idleTimeout.value = null
    }

    // Emit event for UI to show unlock dialog
    window.dispatchEvent(new CustomEvent('session-locked'))
  }

  const unlockSession = async (method: 'pin' | 'biometric', credentials?: any): Promise<boolean> => {
    try {
      if (method === 'pin') {
        // For session unlock, we only need to verify the PIN, not get new tokens
        await verifyPinForUnlock(credentials.pin)
      } else if (method === 'biometric') {
        // For biometric, we assume the authentication was already done in the component
        // In a real implementation, this would verify the biometric credentials
        console.log('🔐 Processing biometric unlock...')

        // Simulate biometric verification (in real app, this would verify the biometric token)
        if (!window.navigator.credentials) {
          throw new Error('Biometric authentication not available')
        }

        // If we reach here, biometric authentication was successful
        console.log('✅ Biometric verification successful')
      }

      isLocked.value = false
      updateActivity()
      console.log('🔓 Session unlocked successfully')
      return true
    } catch (error) {
      console.error('❌ Session unlock failed:', error)
      return false
    }
  }

  // Verify PIN for session unlock (doesn't change tokens)
  const verifyPinForUnlock = async (pin: string) => {
    try {
      // Get current user ID from stored user data
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      // Call PIN verification endpoint (should not return new tokens)
      const response = await authService.verifyPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      if (!response.success) {
        throw new Error('PIN verification failed')
      }

      console.log('✅ PIN verified successfully for session unlock')
      return true
    } catch (err: any) {
      console.error('❌ PIN verification failed:', err)
      throw new Error(err.message || 'PIN verification failed')
    }
  }

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
  }

  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.login(credentials)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      // Start idle timeout after successful login
      updateActivity()

      return response
    } catch (err: any) {
      setError(err.message || 'Login failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.register(userData)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      return response
    } catch (err: any) {
      setError(err.message || 'Registration failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true
      
      // Call logout API
      await authService.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // Clear local state regardless of API call result
      user.value = null
      clearTokens()
      clearError()
      isLoading.value = false

      // Clear idle timeout
      isLocked.value = false
      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authService.refreshToken(refreshToken.value)
      token.value = response.token
      localStorage.setItem('auth_token', response.token)

      return response.token
    } catch (err) {
      // Refresh failed, logout user
      await logout()
      throw err
    }
  }

  const fetchProfile = async () => {
    try {
      isLoading.value = true
      clearError()

      const profile = await authService.getProfile()
      user.value = profile

      return profile
    } catch (err: any) {
      setError(err.message || 'Failed to fetch profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      clearError()

      const updatedUser = await authService.updateProfile(profileData)
      user.value = updatedUser

      return updatedUser
    } catch (err: any) {
      setError(err.message || 'Failed to update profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.verifyEmail({ token })
      
      // Refresh user profile to get updated email_verified status
      if (user.value) {
        await fetchProfile()
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Email verification failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resendVerification = async () => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.resendEmailVerification()
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification email')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const requestPasswordReset = async (email: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.requestPasswordReset({ email })
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to request password reset')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (token: string, password: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.confirmPasswordReset({ token, password })
      return response
    } catch (err: any) {
      setError(err.message || 'Password reset failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.changePassword(currentPassword, newPassword)
      return response
    } catch (err: any) {
      setError(err.message || 'Password change failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Admin PIN authentication
  const loginWithPin = async (pin: string) => {
    try {
      isLoading.value = true
      clearError()

      // Get current user ID from stored user data or localStorage
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      const response = await authService.loginWithPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      user.value = response.user
      setTokens(response.accessToken, response.refreshToken)

      return { success: true, user: response.user, authMethod: 'pin' }
    } catch (err: any) {
      setError(err.message || 'PIN authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric authentication
  const loginWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric login
        const response = await authService.loginWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-login-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric login
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric authentication failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric session unlock (requires existing session)
  const unlockWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if user session exists
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric unlock
        const response = await authService.unlockWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-unlock-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric unlock
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric unlock failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric unlock failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Setup biometric authentication
  const setupBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available on this device')
      }

      const credential = await biometricService.register()

      // Register the credential with the backend
      const response = await authService.registerBiometric({
        credentialId: credential.id,
        publicKey: btoa(JSON.stringify(credential.response)), // Convert to base64
        deviceType: 'platform',
        deviceName: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
        transport: ['internal'],
        deviceFingerprint: await generateDeviceFingerprint()
      })

      if (response.success) {
        localStorage.setItem('biometric_credential_id', credential.id)
        return { success: true, credential, credentialId: response.credentialId }
      } else {
        throw new Error(response.message || 'Failed to register biometric credential')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric setup failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Check biometric capabilities
  const getBiometricCapabilities = async () => {
    try {
      return await biometricService.getCapabilities()
    } catch (err: any) {
      console.warn('Failed to get biometric capabilities:', err)
      return {
        supported: false,
        available: false,
        authenticators: [],
        hasCredentials: false
      }
    }
  }

  // Generate device fingerprint for security
  const generateDeviceFingerprint = async () => {
    // Generate a simple device fingerprint
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('Device fingerprint', 10, 10)

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // Simple hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36)
  }

  // Initialize auth state on app start
  const initializeAuth = async () => {
    if (token.value && !user.value) {
      try {
        await fetchProfile()
      } catch (err) {
        // Token is invalid, clear it
        clearTokens()
      }
    }
  }

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    isAdmin,
    isStaff,
    isEmailVerified,
    userInitials,

    // Idle timeout state
    isLocked: readonly(isLocked),
    lastActivity: readonly(lastActivity),

    // Actions
    setError,
    clearError,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchProfile,
    updateProfile,
    verifyEmail,
    resendVerification,
    requestPasswordReset,
    resetPassword,
    changePassword,
    loginWithPin,
    loginWithBiometric,
    unlockWithBiometric,
    setupBiometric,
    getBiometricCapabilities,
    initializeAuth,
    // Idle timeout actions
    updateActivity,
    lockSession,
    unlockSession,
    verifyPinForUnlock,
  }
})
