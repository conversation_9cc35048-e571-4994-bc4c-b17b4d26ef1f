import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authService, type User, type LoginRequest, type RegisterRequest } from '@/services/auth'
import { biometricService } from '@/services/biometric'
import axios from 'axios'

export const useAuthStore = defineStore('auth', () => {
  // API Configuration
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
  const API_VERSION = 'v1'

  // Helper function for authenticated API calls
  const makeAuthenticatedRequest = async (method: 'GET' | 'POST', endpoint: string, data?: any) => {
    const url = `${API_BASE_URL}/api/${API_VERSION}${endpoint}`
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...(token.value && { Authorization: `Bearer ${token.value}` })
      },
      ...(data && { data })
    }

    try {
      const response = await axios(config)
      return response.data
    } catch (error: any) {
      if (error.response?.status === 401) {
        // Don't logout during session lock operations
        if (!endpoint.includes('/session/')) {
          await logout()
        }
      }
      throw error
    }
  }

  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Idle timeout state - secure session lock
  const isLocked = ref(false)
  const lastActivity = ref<Date>(new Date())
  const idleTimeout = ref<NodeJS.Timeout | null>(null)
  const IDLE_TIMEOUT_MINUTES = 5
  const sessionLockTime = ref<number | null>(null)
  const lockToken = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value && !isLocked.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isStaff = computed(() => user.value?.role === 'staff' || isAdmin.value)
  const isEmailVerified = computed(() => user.value?.email_verified || false)
  const userInitials = computed(() => {
    if (!user.value) return ''
    const names = user.value.name.split(' ')
    return names.map(name => name.charAt(0).toUpperCase()).join('')
  })

  // Idle timeout functions
  const updateActivity = () => {
    lastActivity.value = new Date()
    localStorage.setItem('last_activity', Date.now().toString())
    resetIdleTimeout()
  }

  // Check session lock status from server
  const checkSessionLock = async () => {
    if (!token.value || !user.value) {
      return // Not authenticated, no need to lock
    }

    // Don't lock session when offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - skipping session lock check')
      resetIdleTimeout()
      return
    }

    try {
      // Check server-side session status
      const response = await makeAuthenticatedRequest('GET', '/auth/session/status')

      if (response.data.isLocked) {
        console.log('🔒 Server indicates session is locked')
        isLocked.value = true
        sessionLockTime.value = Date.now()

        // Store lock data for unlock process
        if (response.data.lockData?.lockId) {
          // We'll need to get the lock token from the lock process
          console.log('🔑 Session locked with ID:', response.data.lockData.lockId)
        }

        // Emit event for UI to show unlock dialog
        window.dispatchEvent(new CustomEvent('session-locked'))
        return
      }

      // Check local inactivity
      const lastActivityTime = localStorage.getItem('last_activity')
      if (lastActivityTime) {
        const timeSinceLastActivity = Date.now() - parseInt(lastActivityTime)
        const timeoutMs = IDLE_TIMEOUT_MINUTES * 60 * 1000

        if (timeSinceLastActivity > timeoutMs) {
          console.log('🔒 Session should be locked due to inactivity')
          await lockSession('inactivity')
          return
        }
      }

      // If we reach here, session is still valid - start the timeout
      resetIdleTimeout()

    } catch (error: any) {
      console.error('Failed to check session status:', error)

      // If it's a 404 or 500 error, the endpoint might not be implemented yet
      if (error.response?.status === 404 || error.response?.status === 500) {
        console.log('Session status endpoint not available, using local timeout behavior')
      }

      // Fall back to local timeout behavior
      const lastActivityTime = localStorage.getItem('last_activity')
      if (lastActivityTime) {
        const timeSinceLastActivity = Date.now() - parseInt(lastActivityTime)
        const timeoutMs = IDLE_TIMEOUT_MINUTES * 60 * 1000

        if (timeSinceLastActivity > timeoutMs) {
          console.log('🔒 Session should be locked due to inactivity (local check)')
          await lockSession('inactivity')
          return
        }
      }

      resetIdleTimeout()
    }
  }

  const resetIdleTimeout = () => {
    if (idleTimeout.value) {
      clearTimeout(idleTimeout.value)
    }

    // Only set timeout if authenticated, not locked, and online
    if (isAuthenticated.value && !isLocked.value && navigator.onLine) {
      idleTimeout.value = setTimeout(async () => {
        // Double-check online status before locking
        if (navigator.onLine) {
          await lockSession('inactivity')
        } else {
          console.log('🌐 Offline detected during timeout - not locking session')
          // Reset timeout for when we come back online
          resetIdleTimeout()
        }
      }, IDLE_TIMEOUT_MINUTES * 60 * 1000)
    }
  }

  const lockSession = async (reason: 'inactivity' | 'manual' | 'security' = 'manual') => {
    // Don't lock if offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - not locking session')
      return
    }

    try {
      // Call server-side lock endpoint
      const response = await makeAuthenticatedRequest('POST', '/auth/session/lock', { reason })

      if (response.success) {
        isLocked.value = true
        sessionLockTime.value = Date.now()
        lockToken.value = response.data.lockToken

        if (idleTimeout.value) {
          clearTimeout(idleTimeout.value)
          idleTimeout.value = null
        }

        console.log('🔒 Session locked securely on server:', reason)

        // Emit event for UI to show unlock dialog
        window.dispatchEvent(new CustomEvent('session-locked'))
      } else {
        console.error('Failed to lock session on server:', response.error)
      }
    } catch (error: any) {
      console.error('Error locking session:', error)

      // If server-side lock fails, fall back to local lock
      console.log('🔒 Falling back to local session lock')
      isLocked.value = true
      sessionLockTime.value = Date.now()
      lockToken.value = null // No server token available

      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }

      window.dispatchEvent(new CustomEvent('session-locked'))
    }
  }

  const unlockSession = async (method: 'pin' | 'biometric', credentials?: any): Promise<boolean> => {
    try {
      if (!lockToken.value) {
        console.warn('No lock token available, attempting local unlock')
        // Fall back to local verification for backward compatibility
        if (method === 'pin') {
          await verifyPinForUnlock(credentials.pin)
        }

        isLocked.value = false
        sessionLockTime.value = null
        updateActivity()
        return true
      }

      // Call server-side unlock endpoint
      const response = await makeAuthenticatedRequest('POST', '/auth/session/unlock', {
        lockToken: lockToken.value,
        method,
        credentials
      })

      if (response.success) {
        isLocked.value = false
        sessionLockTime.value = null
        lockToken.value = null

        updateActivity()
        console.log('🔓 Session unlocked successfully via server')
        return true
      } else {
        throw new Error(response.error?.message || 'Server unlock failed')
      }

    } catch (error) {
      console.error('Session unlock failed:', error)

      // For backward compatibility and offline scenarios, try local verification
      try {
        if (method === 'pin') {
          await verifyPinForUnlock(credentials.pin)
        } else if (method === 'biometric') {
          // Simplified biometric verification for offline
          console.log('🔐 Offline biometric unlock')
          if (!window.navigator.credentials) {
            throw new Error('Biometric authentication not available')
          }
        }

        isLocked.value = false
        sessionLockTime.value = null
        lockToken.value = null

        updateActivity()
        console.log('🔓 Session unlocked locally (offline mode)')
        return true

      } catch (localError) {
        console.error('Local unlock also failed:', localError)
        return false
      }
    }
  }

  // Verify PIN for session unlock (doesn't change tokens)
  const verifyPinForUnlock = async (pin: string) => {
    try {
      // Get current user ID from stored user data
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      console.log('🔐 Verifying PIN for session unlock...')

      // Call PIN verification endpoint (should not return new tokens)
      const response = await authService.verifyPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      console.log('📡 PIN verification response:', response)

      // Check if response exists and has success property
      if (!response || response.success !== true) {
        throw new Error('PIN verification failed')
      }

      console.log('✅ PIN verified successfully for session unlock')
      return true
    } catch (err: any) {
      console.error('❌ PIN verification failed:', err)
      // If it's a network error or API error, provide more context
      if (err.response?.status === 404) {
        throw new Error('PIN verification endpoint not found. Please check backend configuration.')
      } else if (err.response?.status === 401) {
        throw new Error('Invalid PIN. Please try again.')
      } else {
        throw new Error(err.message || 'PIN verification failed')
      }
    }
  }

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
  }

  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.login(credentials)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      // Start idle timeout after successful login
      updateActivity()

      return response
    } catch (err: any) {
      setError(err.message || 'Login failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.register(userData)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      return response
    } catch (err: any) {
      setError(err.message || 'Registration failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true
      
      // Call logout API
      await authService.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // Clear local state regardless of API call result
      user.value = null
      clearTokens()
      clearError()
      isLoading.value = false

      // Clear idle timeout and session lock
      isLocked.value = false
      sessionLockTime.value = null
      lockToken.value = null
      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authService.refreshToken(refreshToken.value)
      token.value = response.token
      localStorage.setItem('auth_token', response.token)

      return response.token
    } catch (err) {
      // Refresh failed, logout user
      await logout()
      throw err
    }
  }

  const fetchProfile = async () => {
    try {
      isLoading.value = true
      clearError()

      const profile = await authService.getProfile()
      user.value = profile

      return profile
    } catch (err: any) {
      setError(err.message || 'Failed to fetch profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      clearError()

      const updatedUser = await authService.updateProfile(profileData)
      user.value = updatedUser

      return updatedUser
    } catch (err: any) {
      setError(err.message || 'Failed to update profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.verifyEmail({ token })
      
      // Refresh user profile to get updated email_verified status
      if (user.value) {
        await fetchProfile()
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Email verification failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resendVerification = async () => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.resendEmailVerification()
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification email')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const requestPasswordReset = async (email: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.requestPasswordReset({ email })
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to request password reset')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (token: string, password: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.confirmPasswordReset({ token, password })
      return response
    } catch (err: any) {
      setError(err.message || 'Password reset failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.changePassword(currentPassword, newPassword)
      return response
    } catch (err: any) {
      setError(err.message || 'Password change failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Admin PIN authentication
  const loginWithPin = async (pin: string) => {
    try {
      isLoading.value = true
      clearError()

      // Get current user ID from stored user data or localStorage
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      const response = await authService.loginWithPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      user.value = response.user
      setTokens(response.accessToken, response.refreshToken)

      return { success: true, user: response.user, authMethod: 'pin' }
    } catch (err: any) {
      setError(err.message || 'PIN authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric authentication
  const loginWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric login
        const response = await authService.loginWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-login-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric login
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric authentication failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric session unlock (requires existing session)
  const unlockWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if user session exists
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric unlock
        const response = await authService.unlockWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-unlock-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric unlock
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric unlock failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric unlock failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Setup biometric authentication
  const setupBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available on this device')
      }

      const credential = await biometricService.register()

      // Register the credential with the backend
      const response = await authService.registerBiometric({
        credentialId: credential.id,
        publicKey: btoa(JSON.stringify(credential.response)), // Convert to base64
        deviceType: 'platform',
        deviceName: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
        transport: ['internal'],
        deviceFingerprint: await generateDeviceFingerprint()
      })

      if (response.success) {
        localStorage.setItem('biometric_credential_id', credential.id)
        return { success: true, credential, credentialId: response.credentialId }
      } else {
        throw new Error(response.message || 'Failed to register biometric credential')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric setup failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Check biometric capabilities
  const getBiometricCapabilities = async () => {
    try {
      return await biometricService.getCapabilities()
    } catch (err: any) {
      console.warn('Failed to get biometric capabilities:', err)
      return {
        supported: false,
        available: false,
        authenticators: [],
        hasCredentials: false
      }
    }
  }

  // Generate device fingerprint for security
  const generateDeviceFingerprint = async () => {
    // Generate a simple device fingerprint
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('Device fingerprint', 10, 10)

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // Simple hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36)
  }

  // Initialize auth state on app start
  const initializeAuth = async () => {
    if (token.value && !user.value) {
      try {
        await fetchProfile()
      } catch (err) {
        // Token is invalid, clear it
        clearTokens()
      }
    }

    // Check if session should be locked based on inactivity
    if (token.value && user.value) {
      await checkSessionLock()
    }

    // Set up network status listeners
    setupNetworkListeners()
  }

  const setupNetworkListeners = () => {
    // Handle coming back online
    const handleOnline = () => {
      console.log('🌐 Network back online - resuming session timeout')
      if (isAuthenticated.value && !isLocked.value) {
        resetIdleTimeout()
      }
    }

    // Handle going offline
    const handleOffline = () => {
      console.log('🌐 Network offline - pausing session timeout')
      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }
    }

    // Add event listeners
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden - user might be trying to bypass lock
        console.log('📱 Page hidden - updating last activity')
        updateActivity()
      } else {
        // Page is visible again - check if lock is required
        console.log('📱 Page visible - checking session lock status')
        if (isAuthenticated.value) {
          checkSessionLock()
        }
      }
    }

    // Add visibility listener
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Store cleanup function
    window.authNetworkCleanup = () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    isAdmin,
    isStaff,
    isEmailVerified,
    userInitials,

    // Idle timeout state
    isLocked: readonly(isLocked),
    lastActivity: readonly(lastActivity),

    // Actions
    setError,
    clearError,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchProfile,
    updateProfile,
    verifyEmail,
    resendVerification,
    requestPasswordReset,
    resetPassword,
    changePassword,
    loginWithPin,
    loginWithBiometric,
    unlockWithBiometric,
    setupBiometric,
    getBiometricCapabilities,
    initializeAuth,
    // Idle timeout actions
    updateActivity,
    lockSession,
    unlockSession,
    verifyPinForUnlock,
    checkSessionLock,
  }
})
