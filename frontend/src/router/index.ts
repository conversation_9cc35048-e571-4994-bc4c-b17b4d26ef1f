import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import AboutView from '../views/AboutView.vue'
import ServicesView from '../views/ServicesView.vue'
import ContactView from '../views/ContactView.vue'
import DashboardView from '../views/DashboardView.vue'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: AboutView,
    },
    {
      path: '/services',
      name: 'services',
      component: ServicesView,
    },
    {
      path: '/contact',
      name: 'contact',
      component: ContactView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../components/admin/AdminLogin.vue'),
      meta: { title: 'Admin Login', hideNavigation: true }
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/theme-demo',
      name: 'theme-demo',
      component: () => import('../views/ThemeDemo.vue'),
    },
    {
      path: '/socket-test',
      name: 'socket-test',
      component: () => import('../components/admin/SocketTest.vue'),
    },
    {
      path: '/admin/socket-monitor',
      name: 'socket-monitor',
      component: () => import('../components/admin/SocketMonitor.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/logs',
      name: 'admin-logs',
      component: () => import('../components/admin/SystemLogs.vue'),
      meta: { requiresAuth: true, requiresAdmin: true, title: 'System Logs' }
    },

    {
      path: '/crm',
      name: 'crm',
      redirect: '/crm/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'crm-dashboard',
          component: () => import('../components/crm/CRMDashboard.vue')
        },
        {
          path: 'customers',
          name: 'crm-customers',
          component: () => import('../components/crm/CustomerList.vue')
        },
        {
          path: 'customers/:id',
          name: 'crm-customer-detail',
          component: () => import('../components/crm/CustomerDetail.vue')
        },
        {
          path: 'projects',
          name: 'crm-projects',
          component: () => import('../components/crm/ProjectList.vue')
        },
        {
          path: 'communications',
          name: 'crm-communications',
          component: () => import('../components/crm/CommunicationHub.vue')
        },
        {
          path: 'communications/new',
          name: 'crm-communication-new',
          component: () => import('../views/crm/CommunicationForm.vue')
        }
      ]
    }
  ],
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Wait for auth initialization if we have a token but no user data
  if (authStore.token && !authStore.user) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.warn('Auth initialization failed:', error)
      // Continue with navigation, auth state will be cleared
    }
  }

  // Define login/auth pages that authenticated users shouldn't access
  const authPages = ['login', 'register', 'admin-login']

  // If user is authenticated and trying to access login/register pages
  if (authStore.isAuthenticated && authPages.includes(to.name as string)) {
    // Check if there's a redirect query parameter from the original request
    const redirectPath = to.query.redirect as string

    if (redirectPath) {
      // Redirect to the originally requested page
      next(redirectPath)
    } else {
      // Redirect based on user role
      if (authStore.isAdmin && to.name === 'admin-login') {
        next({ name: 'admin' })
      } else if (to.name === 'admin-login') {
        // Non-admin trying to access admin login, redirect to regular dashboard
        next({ name: 'dashboard' })
      } else {
        // Regular login/register, redirect to dashboard
        next({ name: 'dashboard' })
      }
    }
    return
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // Redirect to appropriate login page
    if (to.meta.requiresAdmin) {
      next({ name: 'admin-login', query: { redirect: to.fullPath } })
    } else {
      next({ name: 'login', query: { redirect: to.fullPath } })
    }
  } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
    // Redirect to admin login if not admin
    next({ name: 'admin-login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

// Handle navigation errors (especially for offline scenarios)
router.onError((error) => {
  console.error('Router navigation error:', error)

  // Check if it's a dynamic import error (likely offline)
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.log('Dynamic import failed - likely offline, redirecting to cached pages')

    // Check if we're offline
    if (!navigator.onLine) {
      // Redirect to offline page or cached page
      const cachedPages = ['/', '/about', '/services', '/contact', '/dashboard']
      const currentPath = window.location.pathname

      if (cachedPages.includes(currentPath)) {
        // Try to reload the current page
        window.location.reload()
      } else {
        // Redirect to home page which should be cached
        router.replace('/')
      }
    }
  }
})

export default router
