<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { RouterView, useRouter, useRoute } from 'vue-router'
import DefaultLayout from './layouts/DefaultLayout.vue'
import PWAInstallPrompt from './components/PWAInstallPrompt.vue'
import PWAUpdatePrompt from './components/PWAUpdatePrompt.vue'
import NetworkStatus from './components/common/NetworkStatus.vue'
import WhatsAppButton from './components/common/WhatsAppButton.vue'
import VersionInfo from './components/common/VersionInfo.vue'
import ToastContainer from './components/common/ToastContainer.vue'
import SessionLock from './components/auth/SessionLock.vue'
import { usePWA } from './composables/usePWA'
import { useOfflineStore } from './stores/offline'
import { useOfflineCache } from './composables/useOfflineCache'
import { useSEO } from './composables/useSEO'
import { useActivityTracker } from './composables/useActivityTracker'
import { getGlobalSocket } from './plugins/socket'

const router = useRouter()
const route = useRoute()
const { initializePWA } = usePWA()
const offlineStore = useOfflineStore()
const { initializeCache, trackPageVisit } = useOfflineCache()
const { updateSEO } = useSEO()

// Initialize activity tracking for idle timeout
useActivityTracker()

// Check if current route is admin-related
const isAdminRoute = computed(() => {
  return route.path.startsWith('/admin')
})

// Get global Socket.io instance for analytics
const globalSocket = getGlobalSocket()

// Initialize PWA features
initializePWA()
offlineStore.initializeOfflineStore()

onMounted(() => {
  // Initialize SEO
  updateSEO()

  // Initialize offline cache
  initializeCache()

  // Track page visits for better caching and analytics
  router.afterEach((to) => {
    trackPageVisit(to.path)

    // Track page view with Socket.io analytics
    if (globalSocket && globalSocket.trackPageView) {
      const pageTitle = to.meta?.title as string || document.title || to.name as string || to.path
      globalSocket.trackPageView(to.path, pageTitle)
      console.log(`📊 Tracked page view: ${to.path} - ${pageTitle}`)
    }
  })

  // Handle offline navigation errors
  router.onError((error) => {
    console.error('Router error:', error)

    if (error.message.includes('Failed to fetch dynamically imported module')) {
      console.log('Navigation failed - likely offline')

      if (!navigator.onLine) {
        // Show offline notification
        console.log('Redirecting to cached content')

        // Try to navigate to a cached page
        const cachedPages = ['/', '/about', '/services', '/contact', '/dashboard']
        const currentPath = window.location.pathname

        if (!cachedPages.includes(currentPath)) {
          router.replace('/')
        }
      }
    }
  })
})
</script>

<template>
  <DefaultLayout>
    <RouterView />

    <!-- PWA Components -->
    <PWAInstallPrompt />
    <PWAUpdatePrompt />
    <NetworkStatus />

    <!-- WhatsApp Contact Button (hidden on admin pages) -->
    <WhatsAppButton v-if="!isAdminRoute" />

    <!-- Version Information -->
    <VersionInfo />

    <!-- Toast Notifications -->
    <ToastContainer />

    <!-- Session Lock Modal -->
    <SessionLock />

  </DefaultLayout>
</template>
