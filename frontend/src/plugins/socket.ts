import { watch, nextTick } from 'vue'
import type { App } from 'vue'
import { useRouter } from 'vue-router'
import { useSocket } from '@/composables/useSocket'
import { useAuthStore } from '@/stores/auth'

// Global socket instance
let globalSocket: ReturnType<typeof useSocket> | null = null

// Socket.io plugin for Vue
export default {
  install(app: App) {
    // Create a getter function that initializes socket lazily
    const getSocket = () => {
      if (!globalSocket) {
        console.log('🔌 Initializing global Socket.io instance...')
        globalSocket = useSocket()
        initializeGlobalSocket(app)
      }
      return globalSocket
    }

    // Make socket getter available globally
    app.config.globalProperties.$socket = getSocket

    // Provide socket getter for injection
    app.provide('socket', getSocket)
  }
}

// Initialize global Socket.io features
function initializeGlobalSocket(app: App) {
  if (!globalSocket) return

  const authStore = useAuthStore()

  console.log('🌐 Setting up global Socket.io watchers...')

  // Watch for auth changes to manage connection
  watch(() => authStore.token, (newToken) => {
    if (newToken && globalSocket) {
      console.log('🔄 User authenticated - upgrading Socket.io connection')
      // Reconnect with user token
      globalSocket.disconnect()
      setTimeout(() => globalSocket!.connect(), 100)
    } else if (!newToken && globalSocket) {
      console.log('🔄 User logged out - switching to temp token connection')
      // Will automatically get temp token when connecting
      globalSocket.disconnect()
      setTimeout(() => globalSocket!.connect(), 100)
    }
  })

  // Auto-connect after a short delay to ensure we're in proper context
  setTimeout(async () => {
    if (globalSocket) {
      console.log('🌐 Auto-connecting global Socket.io...')
      await globalSocket.connect()
      console.log('🌐 Global Socket.io initialized and connected')
    }
  }, 100)

  return globalSocket
}

// Auto-connect socket when user is authenticated (legacy function)
export function initializeSocket() {
  return globalSocket || useSocket()
}

// Get global socket instance (safe to call from anywhere)
export function getGlobalSocket() {
  if (!globalSocket) {
    console.log('🔌 Creating global Socket.io instance on demand...')
    globalSocket = useSocket()
  }
  return globalSocket
}

// Composable for components to use global socket
export function useGlobalSocket() {
  return getGlobalSocket()
}

// Socket.io analytics integration
export function useSocketAnalytics() {
  const socket = useSocket()
  const route = useRoute()
  
  // Track page views automatically
  watch(() => route.path, (newPath) => {
    if (socket.isConnected.value) {
      socket.trackPageView(newPath, document.title)
    }
  }, { immediate: true })
  
  // Track clicks on important elements
  const trackClick = (element: string) => {
    if (socket.isConnected.value) {
      socket.trackClick(element, route.path)
    }
  }
  
  // Track form submissions
  const trackFormSubmit = (form: string, success: boolean) => {
    if (socket.isConnected.value) {
      socket.trackFormSubmit(form, success)
    }
  }
  
  return {
    trackClick,
    trackFormSubmit
  }
}

// Socket.io notifications integration
export function useSocketNotifications() {
  const socket = useSocket()
  
  // Request notification permission
  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }
  
  // Show browser notification
  const showBrowserNotification = (title: string, options?: NotificationOptions) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })
    }
  }
  
  return {
    notifications: socket.notifications,
    unreadNotifications: socket.unreadNotifications,
    markAsRead: socket.markNotificationAsRead,
    requestNotificationPermission,
    showBrowserNotification
  }
}

// Socket.io real-time communication
export function useSocketCommunication() {
  const socket = useSocket()
  
  // Join a conversation room
  const joinConversation = (conversationId: string) => {
    socket.joinRoom(`conversation:${conversationId}`)
  }
  
  // Leave a conversation room
  const leaveConversation = (conversationId: string) => {
    socket.leaveRoom(`conversation:${conversationId}`)
  }
  
  // Send a message
  const sendMessage = (conversationId: string, message: string, attachments?: any[]) => {
    socket.sendMessage(conversationId, message, attachments)
  }
  
  // Typing indicators
  const startTyping = (conversationId: string) => {
    socket.startTyping(`conversation:${conversationId}`)
  }
  
  const stopTyping = (conversationId: string) => {
    socket.stopTyping(`conversation:${conversationId}`)
  }
  
  return {
    joinConversation,
    leaveConversation,
    sendMessage,
    startTyping,
    stopTyping,
    typingUsers: socket.typingUsers
  }
}

// Socket.io connection status component
export function useSocketStatus() {
  const socket = useSocket()
  
  const getStatusColor = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'success'
      case 'connecting': return 'warning'
      case 'error': return 'error'
      default: return 'neutral'
    }
  }
  
  const getStatusIcon = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'wifi'
      case 'connecting': return 'refresh'
      case 'error': return 'wifi-off'
      default: return 'wifi-off'
    }
  }
  
  const getStatusText = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'Connected'
      case 'connecting': return 'Connecting...'
      case 'error': return 'Connection Error'
      default: return 'Disconnected'
    }
  }
  
  return {
    status: socket.connectionStatus,
    isConnected: socket.isConnected,
    isConnecting: socket.isConnecting,
    error: socket.connectionError,
    reconnectAttempts: socket.reconnectAttempts,
    lastPing: socket.lastPing,
    getStatusColor,
    getStatusIcon,
    getStatusText,
    connect: socket.connect,
    disconnect: socket.disconnect
  }
}
